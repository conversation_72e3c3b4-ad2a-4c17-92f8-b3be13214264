"""
Visual Generator Agent
---------------------
Generates video content for story segments using Replicate's bytedance/seedance-1-lite model.
"""

import os
import logging
import requests
from typing import List, Dict

import replicate
from crewai import Task, Crew, Process

from models.schema import ShortSegment

from utils.agent_factory import create_rate_limited_agent
from utils.shorts_utils import get_shorts_directories, load_segments_from_directory

logger = logging.getLogger(__name__)


class VideoGeneratorAgent:
    """
    Agent for generating video content from story segments using Replicate's bytedance/seedance-1-lite model.
    
    This agent takes segment narration and creates engaging visual prompts optimized for the
    bytedance/seedance-1-lite model, then generates corresponding video content.
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai", video_resolution: str = "480p"):
        """
        Initialize the VisualGeneratorAgent.

        Args:
            verbose (bool): Enable verbose output from CrewAI agents
            model (str): Model name to use for LLM
            provider (str): Provider to use for LLM
            video_resolution (str): Video resolution (480p or 720p)
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        self.video_resolution = video_resolution
        
        # Validate Replicate API key
        self.replicate_api_key = os.getenv("REPLICATE_API_KEY")
        if not self.replicate_api_key:
            raise ValueError("Missing required REPLICATE_API_KEY for VisualGeneratorAgent")
        
        # Configure replicate client
        os.environ['REPLICATE_API_TOKEN'] = self.replicate_api_key

    def generate_visual_prompt(self, segment: ShortSegment) -> str:
        """
        Generate an engaging visual prompt for a segment using CrewAI agent.

        Args:
            segment (ShortSegment): The segment to generate visual prompt for

        Returns:
            str: Generated visual prompt optimized for bytedance/seedance-1-lite
        """
        # Create the visual prompt generation agent
        visual_agent = create_rate_limited_agent(
            role="Visual Content Creator",
            goal="Generate compelling visual prompts for Hindi story segments that work perfectly with bytedance/seedance-1-lite video generation model",
            backstory="""You are an expert in visual storytelling and AI video generation with deep understanding of 
            the bytedance/seedance-1-lite model capabilities. You specialize in creating visual prompts that translate 
            Hindi documentary narration into engaging visual scenes. You understand how to create prompts that work 
            well with text-to-video models while maintaining cultural authenticity and visual appeal for short-form content.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False,
        )

        # Create the visual prompt generation task
        prompt_task = Task(
            description=f"""
            Create an engaging visual prompt for the following Hindi story segment that will be used with 
            the bytedance/seedance-1-lite video generation model:

            SEGMENT NARRATION:
            {segment.narration}

            SEGMENT DURATION: {segment.estimated_duration_seconds} seconds

            Your task is to:

            1. **Analyze the Narration**: Understand the key visual elements, emotions, and story beats in the Hindi text.

            2. **Create Visual Scene**: Design a compelling visual scene that complements and enhances the narration.

            3. **Optimize for bytedance/seedance-1-lite**: Ensure the prompt works well with this specific model:
               - Use clear, descriptive language
               - Focus on visual elements that can be rendered effectively
               - Avoid overly complex or abstract concepts
               - Include appropriate lighting, composition, and mood

            4. **Cultural Authenticity**: Ensure the visual elements are culturally appropriate and authentic to the story context.

            5. **Short-form Optimization**: Design for vertical video format (9:16 aspect ratio) and short attention spans.

            IMPORTANT GUIDELINES:
            - Keep the prompt concise but descriptive (2-3 sentences maximum)
            - Focus on visual elements that enhance the storytelling
            - Consider the documentary/news style of the content
            - Ensure the visual complements rather than distracts from the narration
            - Use professional, cinematic language
            - Avoid text overlays or complex animations

            Generate ONLY the visual prompt text, nothing else.
            """,
            agent=visual_agent,
            expected_output="A concise, engaging visual prompt (2-3 sentences) optimized for bytedance/seedance-1-lite video generation that complements the Hindi narration."
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[prompt_task],
            agents=[visual_agent],
            verbose=self.verbose,
        )

        try:
            crew_output = crew.kickoff()
            visual_prompt = crew_output.raw.strip()
            
            logger.info(f"Generated visual prompt for segment {segment.short_number}-{segment.segment_number}")
            logger.debug(f"Visual prompt: {visual_prompt}")
            
            return visual_prompt
            
        except Exception as e:
            logger.error(f"Error generating visual prompt for segment {segment.short_number}-{segment.segment_number}: {str(e)}")
            # Fallback to a simple prompt based on the narration
            return f"A professional documentary-style scene that visually represents the story being told, with appropriate lighting and composition for vertical video format."

    def generate_video(self, segment: ShortSegment, visual_prompt: str, output_path: str) -> bool:
        """
        Generate video using Replicate's bytedance/seedance-1-lite model.

        Args:
            segment (ShortSegment): The segment to generate video for
            visual_prompt (str): The visual prompt for video generation
            output_path (str): Path to save the generated video

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Prepare input parameters for bytedance/seedance-1-lite
            input_params = {
                "prompt": visual_prompt,
                "duration": min(segment.estimated_duration_seconds, 10),  # Max 10 seconds for the model
                "resolution": self.video_resolution,
                "aspect_ratio": "9:16"  # Vertical format for shorts
            }

            logger.info(f"Generating video for segment {segment.short_number}-{segment.segment_number}")
            logger.debug(f"Input params: {input_params}")

            # Generate video using Replicate
            output = replicate.run(
                "bytedance/seedance-1-lite",
                input=input_params
            )

            # Download and save the video
            if hasattr(output, 'url'):
                video_url = output.url()
            else:
                video_url = str(output)

            logger.debug(f"Video generated, downloading from: {video_url}")

            # Download the video file
            response = requests.get(video_url, stream=True, timeout=300)  # 5 minute timeout
            response.raise_for_status()

            # Save to file
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"Video saved to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error generating video for segment {segment.short_number}-{segment.segment_number}: {str(e)}")
            return False

    def process_short_segments(self, short_dir: str) -> List[str]:
        """
        Process all segments in a short directory to generate videos.

        Args:
            short_dir (str): Path to the short directory

        Returns:
            List[str]: List of generated video file paths
        """
        # Load segments from the directory
        segments = load_segments_from_directory(short_dir)
        if not segments:
            logger.warning(f"No segments found in {short_dir}")
            return []

        # Create video directory if it doesn't exist
        video_dir = os.path.join(short_dir, 'video')
        os.makedirs(video_dir, exist_ok=True)

        generated_videos = []
        short_name = os.path.basename(short_dir)

        for segment in segments:
            # Generate filename matching the audio file pattern
            video_filename = f"{short_name}_segment_{segment.segment_number}.mp4"
            video_path = os.path.join(video_dir, video_filename)

            # Skip if video already exists (for --continue functionality)
            if os.path.exists(video_path):
                logger.info(f"Video already exists, skipping: {video_path}")
                generated_videos.append(video_path)
                continue

            # Generate visual prompt
            visual_prompt = self.generate_visual_prompt(segment)

            # Generate video
            if self.generate_video(segment, visual_prompt, video_path):
                generated_videos.append(video_path)
            else:
                logger.warning(f"Failed to generate video for segment {segment.short_number}-{segment.segment_number}")

        logger.info(f"Generated {len(generated_videos)} videos for {short_name}")
        return generated_videos

    def process_all_shorts(self, story_dir: str) -> Dict[str, List[str]]:
        """
        Process all shorts in the story directory to generate videos.

        Args:
            story_dir (str): Path to the story directory

        Returns:
            Dict[str, List[str]]: Dictionary mapping short names to lists of generated video paths
        """
        short_dirs = get_shorts_directories(story_dir)
        if not short_dirs:
            logger.warning(f"No shorts directories found in {story_dir}")
            return {}

        all_generated_videos = {}

        for short_dir in short_dirs:
            short_name = os.path.basename(short_dir)
            logger.info(f"Processing videos for {short_name}")

            try:
                generated_videos = self.process_short_segments(short_dir)
                all_generated_videos[short_name] = generated_videos
                
            except Exception as e:
                logger.error(f"Error processing {short_name}: {str(e)}")
                all_generated_videos[short_name] = []

        total_videos = sum(len(videos) for videos in all_generated_videos.values())
        logger.info(f"Video generation completed. Generated {total_videos} videos across {len(short_dirs)} shorts")

        return all_generated_videos
